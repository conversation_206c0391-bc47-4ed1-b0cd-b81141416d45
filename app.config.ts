export default defineAppConfig({
	host: 'https://ljekarneprima.markerheadless.info',
	lang: 'hr',
	baseCompatibility: '1.30.0',
	hapi: {
		tracking: {
			events: ['catalogproduct'], // praćenje proizvoda
		},
	},	
	publish: {
		postsResponseFields: ['id', 'url_without_domain', 'title', 'main_image_thumbs', 'short_description', 'images'],
	},
	catalog: {
 		quickOrder: true,
		productsResponseFields: [
 			'id',
 			'code',
 			'is_available',
 			'priority_details',
 			'discount_percent',
 			'discount_percent_custom',
 			'price_custom',
 			'basic_price',
 			'basic_price_custom',
 			'rate',
 			'url_without_domain',
 			'main_image_thumbs',
 			'main_image_title',
 			'main_image_description',
 			'title',
			'category_title',
 			'is_available',
 			'attributes_special',
 			'extra_price_lowest',
 			'manufacturer_code',
 			'manufacturer_url',
			'manufacturer_url_without_domain',
 			'manufacturer_title',
 			'shopping_cart_code',
 			'status',
 			'feedback_comment_widget',
 			'feedback_rate_widget',
			'unitd_price',
			'unitd_qty',
			'unitd_description',
			'maximum_qty',
			'unit',
			'groupproducts_ids',
			'type',
			'badge_coupons'
 		],
 	},
	google: {
		gtm: {
			env: ['production','development'],
			//gdpr: 'marketing'
		},
		tracking: {
			//gdpr: 'marketing', // gdpr privola koja se koristi za odobrenje praćenja
			debug: true, // logiranje evenata u konzoli
			affiliation: '', // ako nije definirano, koristi se url weba
			viewItemList: {
				name: 'view_item_list',
			},
			addProduct: {
				name: 'add_to_cart',
			},
			beginCheckout: {
				name: '"begin_checkout"',
			},
			viewItem: {
				name: 'view_item',
				hooks: {
					mapData(data) {
						if(!data?.ecommerce?.items?.length) return data;
						const finalData = {
							event: 'view_item',
							ecommerce: {
								currency: data.ecommerce.items[0].currency,
								value: data.ecommerce.items[0].price,
								items: data.ecommerce.items,
							}
						}
						return finalData;
					},
				}
			},
			addToWishlist: {
				name: 'add_to_wishlist',
				hooks: {
					mapData(data) {
						if(!data?.ecommerce?.items?.length) return data;
						const finalData = {
							event: 'add_to_wishlist',
							ecommerce: {
								currency: data.ecommerce.items[0].currency,
								value: data.ecommerce.items[0].price,
								items: data.ecommerce.items,
							}
						}
						return finalData;
					},
				}
			},
			events: ['gdprConsents', 'view_promotion', 'viewItemList', 'viewItem', 'addProduct', 'beginCheckout', 'addShippingInfo', 'addPaymentInfo', 'selectItem', 'selectPromotion', 'removeProduct', 'addToWishlist', 'purchase'], // 'gdprConsents', 'pageView', 'viewCart', 'removeProduct', 'addProduct', 'login', 'purchase', 'refund',
		},
		remarketing: {
			env: ['production'], // u kojem okruženju se šalju eventi (development, production)
			debug: false, // u konzoli se prikazuje poruka sa poslanim eventom i statusom. Koristiti samo u dev fazi
			category: {
				data: ['pagetype', 'id']
			},
			offerdetail: {
				data: ['pagetype', 'id']
			},
			searchresults: {
				data: ['pagetype', 'id']
			},
			conversionintent: {
				data: ['pagetype', 'id', 'total']
			},
			conversion: {
				data: ['pagetype', 'id']
			},
			events: ['home', 'offerdetail', 'searchresults', 'category', 'conversionintent', 'conversion'],
			gdpr: 'marketing',
		}
	},
	meta: {
		viewport: 'width=device-width, initial-scale=1.0',
	},		
	facebook: {
		pixel: {
			env: ['production'],
			apiKey: '510315749046149',
			target: 'head',
			gdpr: 'analytics'
		},
	},
})