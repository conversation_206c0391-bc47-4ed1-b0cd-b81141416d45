<?php

defined('SYSPATH') OR die('No direct access allowed.');

$host = (isset($_SERVER['HTTP_HOST'])) ? $_SERVER['HTTP_HOST'] : str_replace('/var/www/vhosts/ljekarne-prima-farmacia.hr/httpdocs/', 'ljekarne-prima-farmacia.hr', dirname(__FILE__));
if (in_array($host, ['ljekarne-prima-farmacia.hr'])) {
	return [
	    'default' => [
	        'connection' => [
	            'database' => 'ljekarne-prima_web',
                'hostname' => '**************',
                'username' => 'marker-app',
                'password' => 'HNTYj87%*nm91@hnYNm*$kahn7849kN',
	        ],
	    ],
	];
} elseif (in_array($host, ['itbs007.ljekarne-prima-farmacia.hr'])) {
	return [
	    'default' => [
	        'connection' => [
	            'hostname' => '**************',
	            'username' => 'marker-app',
	            'password' => 'HNTYj87%*nm91@hnYNm*$kahn7849kN',
	            'database' => 'ljekarneprima_dev',
	        ],
	    ],
	];	
} elseif (in_array($host, ['ljprima.markerdev.info'])) {
    return [
        'default' => [
            'connection' => [
                'hostname' => '**************', // 005
                'username' => 'marker-app',
                'password' => 'HNTYj87%*nm91@hnYNm*$kahn7849kN',
                'database' => 'ljekarneprima_dev',
            ],
        ],
    ];
} elseif (in_array($host, ['ljekarneprima.markerheadless.info'])) {
    return [
	    'default' => [
	        'connection' => [
	            'hostname' => '***********',
	            'username' => 'marker-app',
	            'password' => 'HNTYj87%*nm91@hnYNm*$kahn7849kN',
	            'database' => 'ljekarneprima_dev2',
	        ],
	    ], 
    ];    

} else {
	return [
	    'default' => [
	        'connection' => [
	            'hostname' => '**************',
	            'username' => 'marker-app',
	            'password' => 'HNTYj87%*nm91@hnYNm*$kahn7849kN',
	            'database' => 'ljekarne-prima_dev',
	        ],
	    ],
	];
}