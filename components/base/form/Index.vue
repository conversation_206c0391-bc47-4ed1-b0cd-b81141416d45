<template>
	<form :class="[{'loading': loading}]" @submit.prevent="onSubmit" v-bind="$attrs">
		<Body class="form-submitting" v-if="loading" />
		{{ values }}--{{ meta }}
		<slot :values="values" :meta="meta" :errors="errors" :resetForm="resetForm" />
		<label :for="'sc' + id">
			<input autocomplete="off" type="text" :id="'sc' + id" name="sc" v-model="securecode" />
		</label>
	</form>
</template>

<script setup>
	import {useForm} from 'vee-validate';
	const {handleSubmit, meta, values, errors, setFieldValue, resetForm} = useForm();
	const {scrollTo} = useDom();
	const props = defineProps({
		loading: {
			type: Boolean,
			default: false,
		},
	});
	const emit = defineEmits(['submit']);
	const securecode = ref();
	const id = Math.round(Math.random() * 1000);

	// return form values to parent component through v-model
	const model = defineModel();
	model.value = {
		values,
		meta,
		errors,
	};

	// on form submit
	const onSubmit = handleSubmit(
		async (values, actions) => {
			if (securecode.value) return;
			emit('submit', {values, actions});
		},
		({errors}) => {
			// scroll to form error element
			if (Object.keys(errors).length > 0) {
				const errorEl = document.querySelector('[data-scroll-to-error]')?.getAttribute('data-scroll-to-error');
				const settings = {
					offset: 100,
				};
				if (errorEl) {
					const pairs = errorEl.split(',');
					if (pairs?.length) {
						pairs.forEach(pair => {
							const [key, value] = pair.split(':');
							if (key) settings[key] = isNaN(Number(value)) ? value : Number(value);
						});
					}
				}
				scrollTo('[data-scroll-to-error]', settings);
			}
		}
	);

	// provide data to child components
	provide('baseFormData', {setFieldValue, values});
</script>

<style scoped>
	label {
		overflow: hidden;
		height: 0;
		width: 0;
		position: absolute;
		left: -99999px;
		border: 0;
	}
</style>
