<template>
	<slot :items="countries" :submitCountry="submitCountry" :selected="selectedCountry" :loading="loading" />
</template>

<script setup>
	const endpoints = useEndpoints();
	const {submitCustomerData, getCartData} = useWebshop();
	const {detectCountryByIP} = useUtils();
	const lang = useLang();
	const props = defineProps({
		ipDetect: {
			type: Boolean,
			default: false,
		},
		default: String,
	});

	const loading = ref(false);
	const countriesList = ref([]);
	onMounted(async () => {
		loading.value = true;
		const countriesData = await useApi(`${endpoints.get('_get_hapi_webshop_country')}`);
		countriesList.value = countriesData?.data?.length ? countriesData.data : [];

		const selectedCountry = getCartData('customer')?.address?.country;
		if (selectedCountry) {
			loading.value = false;
			return;
		}

		let countryCode = props.default || lang.get('code');

		// Use IP detection to initially select country if no country is already selected. If IP detection fails, use default country code
		if (props.ipDetect) {
			const detectedCountryCode = await detectCountryByIP();
			if (detectedCountryCode) countryCode = detectedCountryCode;
		}

		const matchingCountry = countriesList.value.find(country => country.code.toLowerCase() === countryCode);
		if (matchingCountry) await submitCountry(matchingCountry.id);

		loading.value = false;
	});

	const countries = computed(() => {
		const selectedCountry = getCartData('customer')?.address?.country;
		return countriesList.value.map(country => {
			return {
				...country,
				selected: selectedCountry == country.id ? true : false,
			};
		});
	});

	const selectedCountry = computed(() => {
		if (!countries.value?.length) return null;
		return countries.value.find(country => country.selected) || null;
	});

	async function submitCountry(countryId) {
		if (!countryId) {
			return console.error('Country ID not provided');
		}
		loading.value = true;
		const res = await submitCustomerData(
			{
				country: countryId,
			},
			{
				type: 'webshop.customer-country',
			}
		);
		loading.value = false;
		return res;
	}
</script>
