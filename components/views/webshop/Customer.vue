<template>
	<BaseCmsPage v-slot="{page}">
	<Body class="page-checkout-step2" />
		<WebshopCheckoutLayout class="checkout-container">
			<template #header>
				<WebshopCheckoutHeader :step="2" />
			</template>
	
			<template #wcCol1>
				<BaseWebshopCheckout v-slot="{cart}">
					
					<WebshopStep :step="2" />
					<WebshopStep :step="3" />
					<WebshopStep :step="4" />

					<WebshopCheckoutTracking v-if="cart?.parcels[0]?.items?.length" step="2" :cart="cart" />
				</BaseWebshopCheckout>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
</script>

<style lang="less" scoped>
	.wc-step2-col1{
		width: 100%; padding-bottom: 40px;
		:deep(input){
			&.field_error_input, &.field_error_input_radio{background: unset;}
		}
		@media (max-width: @t){padding-bottom: 35px;}
		@media (max-width: @tp){padding-bottom: 30px;}
	}
	.field-zipcode,.field-city,.field-b_zipcode,.field-b_city{display: none!important;}
	.field-first_name,.field-last_name{margin: 0; width: 100%;}
	.phone-tooltip{
		font-size: 14px; line-height: 18px; position: absolute; top: 21px; right: 25px; color: rgba(43,44,55,0.5);
		@media (max-width: @t){right: 20px; top: 16px; font-size: 12px; line-height: 18px;}
		@media (max-width: @m){right: 15px; top: 15px;}
	}
	.field-phone,.field-b_phone{
		input{padding-right: 180px;}
		@media (max-width: @t){
			input{padding-right: 160px;}
		}
		@media(max-width: @tp){
			input{padding-right: 165px;}
		}
	}
	.field-b_same_as_shipping{
		padding-bottom: 3px;
		@media (max-width: @m){padding-bottom: 6px;}
	}
	.field-title{
		position: absolute; top: -35px; left: 0;
		@media (max-width: @m){top: -27px;}
	}
	.field-b_first_name{
		margin-top: 50px;
		@media (max-width: @m){margin-top: 40px;}
	}
	:deep(.field-group-r1){
		display: flex; flex-flow: column;
		.field-b_r1{order: 1;}
		.field-b_company_oib{order: 3;}
		.field-b_company_name{order: 2;}
		.field-b_company_address{order: 4;}
	}

	:deep(.field){
		.autocomplete-container{position: absolute; top: 58px; right: 0; left: 0; box-shadow: rgba(0, 0, 0,.1) 0px 0px 40px 0px; border-radius: 0 0 2px 2px; z-index: 300; border: 0;}
		.ui-autocomplete{
			background: #fff; list-style: none; border-radius: 0 0 2px 2px; width: auto !important; left: 0px !important; right: 0px !important; padding: 0; margin: 0; z-index: 999999 !important; top: 0 !important; max-height: 200px; overflow: auto; font-size: 14px; border: 1px solid var(--borderColor); height: auto!important;
			li{cursor: pointer; color: var(--textColor); padding: 6px 25px!important; border-top: 1px solid var(--borderColor); .transition(background-color); display: flex; flex-flow: column;}
			li:first-child{border: none;}
			.ui-state-focus{background: #f6f6f6;}
		}
		@media (max-width: @t){
			.autocomplete-container{top: 48px;}
		}
		@media (max-width: @m){
			.autocomplete-container{top: 43px;}
			.ui-autocomplete{
				li{padding: 6px 15px!important;}

			}
		}
	}
	.btn-checkout{
		width: 175px; min-width: 175px; margin-right: 0; margin-bottom: 0; margin-top: 5px; width: auto;
		@media (max-width: @t){margin: 5px 0 0 0; min-width: 180px;}
		@media (max-width: @m){min-width: 100%;}
	}
</style>