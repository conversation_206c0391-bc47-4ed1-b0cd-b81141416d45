<template>
	<BaseCmsPage>
	<Body class="page-checkout-step4" />
		<WebshopCheckoutLayout class="wc-payment">
			<template #header>
				<WebshopCheckoutHeader :step="4" />
			</template>

			<template #wcCol1>
				<BaseWebshopCheckout v-slot="{cart}">
					<WebshopStep :step="1" :completed="true" />
					<WebshopStep :step="2" :completed="true" />
					<BaseWebshopPaymentForm class="step4 step-form4 ajax_siteform ajax_siteform_loading form form-animated-label" v-slot="{fields, loading, status, onPaymentUpdate}">
						<div v-if="status?.data?.errors">
							<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
						</div>

						<div class="payment-options">
							<WebshopStep :step="3" :title="true" />
							<div class="field-payment-cnt">
								<template v-for="field in fields" :key="field.value">
									<BaseFormField :item="field">
										<span :class="'field-' + field.code" >
											<BaseFormInput :checked="field.selected" :id="field.code" mode="custom" />
											<label :for="field.code" :class="'label-' + field.code">
												{{ field.title }}
												<div v-show="field.description" class="payment_info" v-html="field.description"></div>
											</label>
										</span>
									</BaseFormField>
								</template>
							</div>
							<button class="btn btn-primary btn-checkout-last g-recaptcha" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="goto_step3_button" tag="span" /></button>
						</div>
					</BaseWebshopPaymentForm>
					<WebshopStep :step="4" />

					<WebshopCheckoutTracking v-if="cart?.parcels[0]?.items?.length" step="4" :cart="cart" />
				</BaseWebshopCheckout>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<script setup>
</script>

<style lang="less" scoped>
	.payment-options{
		padding-bottom: 40px; padding-top: 15px;
		input[type=radio]+label{line-height: 24px;}
		input[type=radio] + label:before{width: 22px; height: 22px;}
		@media (max-width: @t){
			input[type=radio] + label:before{width: 20px; height: 20px;}
		}
		@media (max-width: @m){padding-bottom: 30px;}
	}
	.field-payment-cnt{
		padding-bottom: 15px;
		@media (max-width: @m){padding-bottom: 12px;}
		&>span{
			margin-bottom: 10px; display: block; position: relative;
			input[type=radio]:checked + label{
				.payment_info{display: block;}
			}
			@media (max-width: @t){margin-bottom: 7px;}
			@media (max-width: @tp){margin-bottom: 5px;}
			@media (max-width: @m){margin-bottom: 3px;}
		}
	}
	.payment_info{
		font-size: 16px; line-height: 22px; padding-top: 3px; font-weight: 300; display: none;
		@media (max-width: @t){font-size: 15px; line-height: 21px;}
		@media (max-width: @tp){font-size: 14px; line-height: 18px;}
	}
	.btn-checkout-last{
		margin: 0; min-width: 175px;
		@media (max-width: @t){margin: 5px 0 0 0; min-width: 180px;}
		@media (max-width: @m){min-width: 100%;}
	}
</style>

<style lang="less">
	.page-checkout-step4{
		.wc-step1.completed-step{
			.step_link,.step_link2{padding-top: 0;}
		}
		.wc-subtitle{
			padding-bottom: 15px !important;
			@media (max-width: @t){padding-bottom: 12px !important;}
			@media (max-width: @tp){padding-bottom: 8px !important;}
		}
	}
</style>
