<template>
	<Link rel="preconnect" href="https://fonts.googleapis.com" />
	<Link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
	<Link href="https://fonts.googleapis.com/css2?family=Yantramanav:wght@300;400;500;700&display=swap" rel="stylesheet" />
	
	<Link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
	<Link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<Link rel="shortcut icon" href="/favicon.ico" />
	<Link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
	<Meta name="apple-mobile-web-app-title" content="ljekarne-prima-farmacia" />
	<Link rel="manifest" href="/site.webmanifest" />
	

	<Body :class="{'fixed-header': fixedHeader && showHeader}" />
	<div class="page-wrapper" id="top">
		<template v-if="showHeader">
			<CmsHeader />
		</template>

		dsddf
		<BaseForm v-slot="{values, meta}">
			
			<template v-for="field in fields" :key="field.value">
				<BaseFormField :item="field">
					<BaseFormInput :checked="field.selected" :id="field.code" mode="custom" />
					<label :for="field.code" :class="'label-' + field.code">
						{{ field.title }}
					</label>
				</BaseFormField>
			</template>
		</BaseForm>
		----
		
		
		<slot />

		<template v-if="showHeader">
			<ClientOnly>
				<LazyCmsNewsletter hydrate-on-visible />
			</ClientOnly>
			<CmsFooter />
		</template>
	</div>

	<ClientOnly>
		<div @click="scrollTo('#top')" class="to-top" :class="{'active': showToTopButton}"></div>
		<LazyWebshopAddToCartModal hydrate-on-visible />
		<LazyNewsletterLeaving hydrate-on-visible />
		<LazyGdpr :hydrate-after="1000" /> 
		<BaseThemeUiPageLoadingIndicator bar-color="#55297a" />
		<LazyBaseThemeUiModal v-if="Object.keys(modal.activeModals()).includes('quick')" name="quick" :zoom="false" :mask-closable="true" :svgicons="false" />
	</ClientOnly>
</template>

<script setup>
	const fields = [
		{
			name: 'payment2',
			code: 'payment2',
			type: 'radio',
			value: 'Test1',
			validation: [
				{
					type: 'not_empty',
					value: null,
					error: 'error_not_empty',
				},
			],			
		},
		{
			name: 'payment2',
			code: 'payment3',
			type: 'radio',
			value: 'Test2',
			validation: [
				{
					type: 'not_empty',
					value: null,
					error: 'error_not_empty',
				},
			],			
		}
	]


	const modal = useModal();
	const {scrollTo, onMediaQuery} = useDom();
	const route = useRoute();
	const showHeader = computed(() => {
		if(!route.meta.action) return true;
		return (['login', 'customer', 'shipping', 'payment', 'review_order'].includes(route.meta.action)) ? false : true;
	});
	const fixedHeader = ref(null);
	const showToTopButton = ref(false);
	const onScroll = () => {
		const scroll = window.scrollY;
		if (!document.body.classList.contains('page-checkout')) {
			showToTopButton.value = (scroll > 400) ? true : false;
		}


		if (!document.body.classList.contains('catalog-layout-page')) {
			fixedHeader.value = (scroll > 70) ? true : false;
		}
	};

	onBeforeUnmount(() => {
		window.removeEventListener('scroll', onScroll);
	})
	onMounted(async () => {
		window.addEventListener('scroll', onScroll, {passive: true});
	});
	//rwd

	onMediaQuery({
		query: '(max-width: 980px)',
		leave: () => {
			window.location.reload();
		}
	});

	onMediaQuery({
		query: '(max-width: 760px)',
		leave: () => {
			window.location.reload();
		}
	});	

	const {matches: mobileSmallBreakpoint} = onMediaQuery({query: '(max-width: 760px)'});
	const {matches: mobileBreakpoint} = onMediaQuery({query: '(max-width: 980px)'});
	const {matches: tabletBreakpoint} = onMediaQuery({query: '(max-width: 1250px)'});
	provide('rwd', {tabletBreakpoint, mobileBreakpoint, mobileSmallBreakpoint});

</script>