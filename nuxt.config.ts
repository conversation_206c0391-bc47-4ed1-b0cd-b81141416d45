// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
	extends: ['../nuxt-base'],
	css: ['@/assets/style.less'],
	vite: {
		css: {
			preprocessorOptions: {
				less: {
					additionalData: `@import "@/assets/_vars.less"; @import "@/assets/_mixins.less";`,
				},
			},
		},
		define: {
			__GTM_TRACKING__: JSON.stringify(true),
			__CATALOG_SEO_URL__: JSON.stringify(true),
			__HAPI_TRACKING__: JSON.stringify(true),
			__REMARKETING__: JSON.stringify(true),
			__RECAPTCHA__: JSON.stringify(true),
			__EVENTS__: JSON.stringify(true),
			__ALT_PAYMENTS__: JSON.stringify(true),
		},
	},
	dir: {
		'public': 'media',
	},
	$production: {
		sourcemap: {
			server: false,
			client: false,
		},
		routeRules: {
			'*': {
				swr: 120,
				cache: {base: 'db'},
			},
		},
	},
	nitro: {
		minify: true,
		storage: {
			db: {
				driver: 'redis',
				host: 'redis_ljekarneprima_markerheadless_info_nuxt',
				port: 6379,
				username: 'default',
				password: 'LqBKEDGSBM',
				base: 'ljekarneprima_dev',
			},
		},
		devStorage: {
			db: {
				driver: 'fs',
				base: '.app_cache_data',
			},
		},
	},
	compatibilityDate: '2024-07-23',
});
